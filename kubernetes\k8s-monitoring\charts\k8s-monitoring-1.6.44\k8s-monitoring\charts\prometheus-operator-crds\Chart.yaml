annotations:
  artifacthub.io/links: |
    - name: Chart Source
      url: https://github.com/prometheus-community/helm-charts
  artifacthub.io/maintainers: |
    - name: dacamposol
      email: <EMAIL>
    - name: desaintmartin
      email: <EMAIL>
    - name: <PERSON><PERSON><PERSON><PERSON>
      email: <EMAIL>
apiVersion: v2
appVersion: v0.84.0
dependencies:
- name: crds
  repository: ""
  version: 0.0.0
description: 'A Helm chart that collects custom resource definitions (CRDs) from the
  Prometheus Operator, allowing for seamless integration with GitOps tools '
icon: https://raw.githubusercontent.com/prometheus/prometheus.github.io/master/assets/prometheus_logo-cb55bb5c346.png
keywords:
- prometheus
- crds
kubeVersion: '>=1.16.0-0'
maintainers:
- email: <EMAIL>
  name: dacamposol
  url: https://github.com/dacamposol
- email: <EMAIL>
  name: desaintmartin
  url: https://github.com/desaintmartin
- email: <EMAIL>
  name: QuentinB<PERSON>on
  url: https://github.com/QuentinBisson
- email: <EMAIL>
  name: Jan-Otto Kröpke
  url: https://github.com/jkroepke
name: prometheus-operator-crds
sources:
- https://github.com/prometheus-community/helm-charts
type: application
version: 22.0.1
