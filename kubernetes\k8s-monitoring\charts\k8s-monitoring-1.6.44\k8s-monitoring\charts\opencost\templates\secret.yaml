{{- if or .Values.opencost.prometheus.username .Values.opencost.prometheus.password .Values.opencost.prometheus.bearer_token .Values.opencost.exporter.aws.access_key_id .Values.opencost.exporter.cloudProviderApiKey }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "opencost.prometheus.secretname" . }}
  namespace: {{ include "opencost.namespace" . }}
  labels: {{- include "opencost.labels" . | nindent 4 }}
  {{- with .Values.secretAnnotations }}
  annotations: {{- toYaml . | nindent 4 }}
  {{- end }}
data:
  {{- if .Values.opencost.prometheus.username }}
  {{ .Values.opencost.prometheus.username_key }}: {{ .Values.opencost.prometheus.username | toString | b64enc | quote }}
  {{- end }}
  {{- if .Values.opencost.prometheus.password }}
  {{ .Values.opencost.prometheus.password_key }}: {{ .Values.opencost.prometheus.password | toString | b64enc | quote }}
  {{- end }}
  {{- if .Values.opencost.prometheus.bearer_token }}
  {{ .Values.opencost.prometheus.bearer_token_key }}: {{ .Values.opencost.prometheus.bearer_token | b64enc | quote }}
  {{- end }}
  {{- if .Values.opencost.exporter.aws.access_key_id }}
  AWS_ACCESS_KEY_ID: {{ .Values.opencost.exporter.aws.access_key_id | b64enc | quote }}
  {{- end }}
  {{- if .Values.opencost.exporter.aws.access_key_id }}
  AWS_SECRET_ACCESS_KEY: {{ .Values.opencost.exporter.aws.secret_access_key | b64enc | quote }}
  {{- end }}
  {{- if .Values.opencost.exporter.cloudProviderApiKey }}
  CLOUD_PROVIDER_API_KEY: {{ .Values.opencost.exporter.cloudProviderApiKey | b64enc | quote }}
  {{- end }}
{{- end }}
