{{- if .Values.ingress.enabled -}}
{{- $ingressApiIsStable := eq (include "alloy.ingress.isStable" .) "true" -}}
{{- $ingressSupportsIngressClassName := eq (include "alloy.ingress.supportsIngressClassName" .) "true" -}}
{{- $ingressSupportsPathType := eq (include "alloy.ingress.supportsPathType" .) "true" -}}
{{- $fullName := include "alloy.fullname" . -}}
{{- $servicePort := .Values.ingress.faroPort -}}
{{- $ingressPath := .Values.ingress.path -}}
{{- $ingressPathType := .Values.ingress.pathType -}}
{{- $extraPaths := .Values.ingress.extraPaths -}}
apiVersion: {{ include "alloy.ingress.apiVersion" . }}
kind: Ingress
metadata:
  name: {{ $fullName }}
  namespace: {{ include "alloy.namespace" . }}
  labels:
    {{- include "alloy.labels" . | nindent 4 }}
    app.kubernetes.io/component: networking
    {{- with .Values.ingress.labels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
  {{- with .Values.ingress.annotations }}
  annotations:
    {{- range $key, $value := . }}
    {{ $key }}: {{ tpl $value $ | quote }}
    {{- end }}
  {{- end }}
spec:
  {{- if and $ingressSupportsIngressClassName .Values.ingress.ingressClassName }}
  ingressClassName: {{ .Values.ingress.ingressClassName }}
  {{- end -}}
  {{- with .Values.ingress.tls }}
  tls:
    {{- tpl (toYaml .) $ | nindent 4 }}
  {{- end }}
  rules:
  {{- if .Values.ingress.hosts  }}
  {{- range .Values.ingress.hosts }}
    - host: {{ tpl . $ }}
      http:
        paths:
          {{- with $extraPaths }}
          {{- toYaml . | nindent 10 }}
          {{- end }}
          - path: {{ $ingressPath }}
            {{- if $ingressSupportsPathType }}
            pathType: {{ $ingressPathType }}
            {{- end }}
            backend:
              {{- if $ingressApiIsStable }}
              service:
                name: {{ $fullName }}
                port:
                  number: {{ $servicePort }}
              {{- else }}
              serviceName: {{ $fullName }}
              servicePort: {{ $servicePort }}
              {{- end }}
  {{- end }}
  {{- else }}
    - http:
        paths:
          - backend:
              {{- if $ingressApiIsStable }}
              service:
                name: {{ $fullName }}
                port:
                  number: {{ $servicePort }}
              {{- else }}
              serviceName: {{ $fullName }}
              servicePort: {{ $servicePort }}
              {{- end }}
            {{- with $ingressPath }}
            path: {{ . }}
            {{- end }}
            {{- if $ingressSupportsPathType }}
            pathType: {{ $ingressPathType }}
            {{- end }}
  {{- end -}}
{{- end }}
