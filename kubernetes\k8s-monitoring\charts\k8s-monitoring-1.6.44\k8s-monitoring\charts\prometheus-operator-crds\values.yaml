## Settings for CRDs
##
crds:
  ## annotations add additional annotations to all CRDs
  annotations: {}

  ## alertmanagerconfigs configures the AlertManagerConfig CRD
  alertmanagerconfigs:
    ## enabled defines if the CRD should be installed
    enabled: true

  ## alertmanagers configures the AlertManager CRD
  alertmanagers:
    ## enabled defines if the CRD should be installed
    enabled: true

  ## podmonitors configures the PodMonitor CRD
  podmonitors:
    ## enabled defines if the CRD should be installed
    enabled: true

  ## probes configures the Probe CRD
  probes:
    ## enabled defines if the CRD should be installed
    enabled: true

  ## prometheusagents configures the PrometheusAgent CRD
  prometheusagents:
    ## enabled defines if the CRD should be installed
    enabled: true

  ## prometheuses configures the Prometheus CRD
  prometheuses:
    ## enabled defines if the CRD should be installed
    enabled: true

  ## prometheusrules configures the PrometheusRule CRD
  prometheusrules:
    ## enabled defines if the CRD should be installed
    enabled: true

  ## prometheusrules configures the PrometheusRule CRD
  scrapeconfigs:
    ## enabled defines if the CRD should be installed
    enabled: true

  ## servicemonitors configures the ServiceMonitor CRD
  servicemonitors:
    ## enabled defines if the CRD should be installed
    enabled: true

  ## thanosrulers configures the ThanosRuler CRD
  thanosrulers:
    ## enabled defines if the CRD should be installed
    enabled: true
