{{- if and .Values.networkPolicy.enabled (eq .Values.networkPolicy.flavor "kubernetes") -}}
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: {{ include "alloy.fullname" . }}
  namespace: {{ include "alloy.namespace" . }}
  labels:
    {{- include "alloy.labels" . | nindent 4 }}
    app.kubernetes.io/component: networking
spec:
  podSelector:
    matchLabels:
      {{- include "alloy.selectorLabels" . | nindent 6 }}
  policyTypes:
    {{- toYaml .Values.networkPolicy.policyTypes | nindent 4 }}
  {{- if .Values.networkPolicy.ingress }}
  ingress:
    {{- toYaml .Values.networkPolicy.ingress | nindent 4 }}
  {{- end }}
  {{- if .Values.networkPolicy.egress }}
  egress:
    {{- toYaml .Values.networkPolicy.egress | nindent 4 }}
  {{- end }}
{{- end }}
