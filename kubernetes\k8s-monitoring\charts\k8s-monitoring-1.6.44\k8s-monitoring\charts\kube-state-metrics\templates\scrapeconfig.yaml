{{- if .Values.prometheus.scrapeconfig.enabled }}
apiVersion: monitoring.coreos.com/v1alpha1
kind: ScrapeConfig
metadata:
  name: {{ template "kube-state-metrics.fullname" . }}
  namespace: {{ template "kube-state-metrics.namespace" . }}
  labels:
    {{- include "kube-state-metrics.labels" . | indent 4 }}
  {{- with .Values.prometheus.scrapeconfig.additionalLabels }}
    {{- tpl (toYaml . | nindent 4) $ }}
  {{- end }}
  {{- with .Values.prometheus.scrapeconfig.annotations }}
  annotations:
    {{- tpl (toYaml . | nindent 4) $ }}
  {{- end }}
spec:
  {{- include "scrapeconfig.scrapeLimits" .Values.prometheus.scrapeconfig | indent 2 }}
  staticConfigs:
    - targets:
        - {{ template "kube-state-metrics.fullname" . }}.{{ template "kube-state-metrics.namespace" . }}.svc:{{ .Values.service.port }}
    {{- if .Values.prometheus.scrapeconfig.staticConfigLabels}}
      labels:
      {{- with .Values.prometheus.scrapeconfig.staticConfigLabels }}
        {{- tpl (toYaml . | nindent 8) $ }}
      {{- end }}
    {{- end }}
{{- if .Values.prometheus.scrapeconfig.jobName }}
  jobName: {{ .Values.prometheus.scrapeconfig.jobName }}
{{- end }}
{{- if .Values.prometheus.scrapeconfig.honorLabels }}
  honorLabels: true
{{- end }}
{{- if .Values.prometheus.scrapeconfig.scrapeInterval }}
  scrapeInterval: {{ .Values.prometheus.scrapeconfig.scrapeInterval }}
{{- end }}
{{- if .Values.prometheus.scrapeconfig.scrapeTimeout }}
  scrapeTimeout: {{ .Values.prometheus.scrapeconfig.scrapeTimeout }}
{{- end }}
{{- if .Values.prometheus.scrapeconfig.proxyUrl }}
  proxyUrl: {{ .Values.prometheus.scrapeconfig.proxyUrl }}
{{- end }}
{{- if .Values.prometheus.scrapeconfig.enableHttp2 }}
  enableHttp2: {{ .Values.prometheus.scrapeconfig.enableHttp2 }}
{{- end }}
{{- if .Values.prometheus.scrapeconfig.metricRelabelings }}
  metricRelabelings:
    {{- toYaml .Values.prometheus.scrapeconfig.metricRelabelings | nindent 4 }}
{{- end }}
{{- if .Values.prometheus.scrapeconfig.relabelings }}
  relabelings:
    {{- toYaml .Values.prometheus.scrapeconfig.relabelings | nindent 4 }}
{{- end }}
{{- if .Values.prometheus.scrapeconfig.scheme  }}
  scheme: {{ .Values.prometheus.scrapeconfig.scheme }}
{{- end }}
{{- if .Values.prometheus.scrapeconfig.tlsConfig }}
  tlsConfig:
    {{- toYaml (.Values.prometheus.scrapeconfig.tlsConfig ) | nindent 4 }}
{{- end }}
{{- end }}
