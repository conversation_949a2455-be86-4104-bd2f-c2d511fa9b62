{{- if .Values.kubeRBACProxy.enabled}}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ template "kube-state-metrics.fullname" . }}-rbac-config
  namespace: {{ template "kube-state-metrics.namespace" . }}
  labels:
    {{- include "kube-state-metrics.labels" . | indent 4 }}
  {{- if .Values.annotations }}
  annotations:
    {{ toYaml .Values.annotations | nindent 4 }}
  {{- end }}
data:
  config-file.yaml: |+
    authorization:
      resourceAttributes:
        namespace: {{ template "kube-state-metrics.namespace" . }}
        apiVersion: v1
        resource: services
        subresource: {{ template "kube-state-metrics.fullname" . }}
        name: {{ template "kube-state-metrics.fullname" . }}
{{- end }}
