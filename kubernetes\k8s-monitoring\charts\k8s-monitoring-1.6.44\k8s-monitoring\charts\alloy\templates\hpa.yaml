{{- $values := (mustMergeOverwrite .Values.alloy (or .Values.agent dict)) -}}
{{- if and (or (eq .Values.controller.type "deployment") (eq .Values.controller.type "statefulset" )) (or .Values.controller.autoscaling.horizontal.enabled .Values.controller.autoscaling.enabled)  }}
{{ $autoscaling := .Values.controller.autoscaling }}
{{- if .Values.controller.autoscaling.horizontal.enabled }}
{{- $autoscaling = .Values.controller.autoscaling.horizontal }}
{{- end }}
{{- if (not (empty $autoscaling.targetMemoryUtilizationPercentage)) }}
  {{- $_ := $values.resources.requests | required ".Values.alloy.resources.requests is required when using autoscaling." -}}
  {{- $_ := $values.resources.requests.memory | required ".Values.alloy.resources.requests.memory is required when using autoscaling based on memory utilization." -}}
  {{- $_ := .Values.configReloader.resources.requests  | required ".Values.configReloader.resources.requests is required when using autoscaling." -}}
  {{- $_ := .Values.configReloader.resources.requests.memory  | required ".Values.configReloader.resources.requests.memory is required when using autoscaling based on memory utilization." -}}
{{- end}}
{{- if (not (empty $autoscaling.targetCPUUtilizationPercentage)) }}
  {{- $_ := $values.resources.requests | required ".Values.alloy.resources.requests is required when using autoscaling." -}}
  {{- $_ := $values.resources.requests.cpu | required ".Values.alloy.resources.requests.cpu is required when using autoscaling based on cpu utilization." -}}
  {{- $_ := .Values.configReloader.resources.requests  | required ".Values.configReloader.resources.requests is required when using autoscaling." -}}
  {{- $_ := .Values.configReloader.resources.requests.cpu  | required ".Values.configReloader.resources.requests.cpu is required when using autoscaling based on cpu utilization." -}}
{{- end}}
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: {{ include "alloy.fullname" . }}
  namespace: {{ include "alloy.namespace" . }}
  labels:
    {{- include "alloy.labels" . | nindent 4 }}
    app.kubernetes.io/component: availability
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: {{ .Values.controller.type  }}
    name: {{ include "alloy.fullname" . }}
  {{- with $autoscaling }}
  minReplicas: {{ .minReplicas }}
  maxReplicas: {{ .maxReplicas }}
  behavior:
    {{- with .scaleDown }}
    scaleDown:
      {{- if .policies }}
      policies:
        {{- range .policies }}
        - type: {{ .type }}
          value: {{ .value }}
          periodSeconds: {{ .periodSeconds }}
        {{- end }}
      selectPolicy: {{ .selectPolicy }}
      {{- end }}
      stabilizationWindowSeconds: {{ .stabilizationWindowSeconds }}
    {{- end }}
    {{- with .scaleUp }}
    scaleUp:
      {{- if .policies }}
      policies:
        {{- range .policies }}
        - type: {{ .type }}
          value: {{ .value }}
          periodSeconds: {{ .periodSeconds }}
        {{- end }}
      selectPolicy: {{ .selectPolicy }}
      {{- end }}
      stabilizationWindowSeconds: {{ .stabilizationWindowSeconds }}
    {{- end }}
  metrics:
    # Changing the order of the metrics will cause ArgoCD to go into a sync loop
    # memory needs to be first.
    # More info in: https://github.com/argoproj/argo-cd/issues/1079
    {{- with .targetMemoryUtilizationPercentage }}
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageUtilization: {{ . }}
    {{- end }}
    {{- with .targetCPUUtilizationPercentage }}
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: {{ . }}
    {{- end }}
  {{- end }}
{{- end }}
