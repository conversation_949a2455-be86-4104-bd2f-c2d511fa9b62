{{- define "alloy.watch-container" -}}
{{- $values := (mustMergeOverwrite .Values.alloy (or .Values.agent dict)) -}}
{{- if .Values.configReloader.enabled -}}
- name: config-reloader
  image: {{ .Values.global.image.registry | default .Values.configReloader.image.registry }}/{{ .Values.configReloader.image.repository }}{{ include "config-reloader.imageId" . }}
  {{- if .Values.configReloader.customArgs }}
  args:
    {{- toYaml .Values.configReloader.customArgs | nindent 4 }}
  {{- else }}
  args:
    - --watched-dir=/etc/alloy
    - --reload-url=http://localhost:{{ $values.listenPort }}/-/reload
  {{- end }}
  volumeMounts:
    - name: config
      mountPath: /etc/alloy
  {{- with .Values.configReloader.resources }}
  resources:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  {{- with .Values.configReloader.securityContext }}
  securityContext:
    {{- toYaml . | nindent 4 }}
  {{- end }}
{{- end }}
{{- end -}}
