{{- if and .Values.k8sCache.replicas (gt (int .Values.k8sCache.replicas) 0) }}
{{- $root := . }}
apiVersion: v1
kind: Service
metadata:
  name: {{ .Values.k8sCache.service.name }}
  namespace: {{ include "beyla.namespace" .}}
  labels:
    {{- include "beyla.cache.labels" . | nindent 4 }}
    app.kubernetes.io/component: networking
    {{- with .Values.k8sCache.service.labels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
  {{- with .Values.service.annotations }}
  annotations:
    {{- tpl (toYaml . | nindent 4) $root }}
  {{- end }}
spec:
  ports:
    - port: {{ .Values.k8sCache.service.port }}
      protocol: TCP
      targetPort: grpc
      name: grpc
  selector:
    app.kubernetes.io/name: {{ .Values.k8sCache.service.name }}
{{- end }}
