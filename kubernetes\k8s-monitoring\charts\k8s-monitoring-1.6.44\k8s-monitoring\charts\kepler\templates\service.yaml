---
apiVersion: v1
kind: Service
metadata:
  name: {{ include "kepler.fullname" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "kepler.labels" . | nindent 4 }}
  {{- with .Values.service.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - name: http
      port: {{ .Values.service.port }}
      targetPort: http
      protocol: TCP
  selector:
    {{- include "kepler.selectorLabels" . | nindent 4 }}
