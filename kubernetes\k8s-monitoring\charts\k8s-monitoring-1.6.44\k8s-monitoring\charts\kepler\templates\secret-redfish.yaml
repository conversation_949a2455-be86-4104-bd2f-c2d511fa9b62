{{- if .Values.redfish.enabled }}
{{- with .Values.redfish }}
---
apiVersion: v1
kind: Secret
metadata:
  name: {{ .name }}
  namespace: {{ $.Release.Namespace }}
  labels:
    {{- include "kepler.labels" $ | nindent 4 }}
    {{- with .labels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
  {{- with .annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
type: Opaque
stringData:
  redfish.csv: {{- .fileContent | toYaml | indent 2 }}
{{- end }}
{{- end }}
