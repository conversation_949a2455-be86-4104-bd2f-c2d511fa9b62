{{- if .Values.podmonitors.enabled -}}
# https://raw.githubusercontent.com/prometheus-operator/prometheus-operator/v0.84.0/example/prometheus-operator-crd/monitoring.coreos.com_podmonitors.yaml
---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
{{- with .Values.annotations }}
{{- toYaml . | nindent 4 }}
{{- end }}
    controller-gen.kubebuilder.io/version: v0.18.0
    operator.prometheus.io/version: 0.84.0
  name: podmonitors.monitoring.coreos.com
spec:
  group: monitoring.coreos.com
  names:
    categories:
    - prometheus-operator
    kind: PodMonitor
    listKind: PodMonitorList
    plural: podmonitors
    shortNames:
    - pmon
    singular: podmonitor
  scope: Namespaced
  versions:
  - name: v1
    schema:
      openAPIV3Schema:
        description: |-
          The `PodMonitor` custom resource definition (CRD) defines how `Prometheus` and `PrometheusAgent` can scrape metrics from a group of pods.
          Among other things, it allows to specify:
          * The pods to scrape via label selectors.
          * The container ports to scrape.
          * Authentication credentials to use.
          * Target and metric relabeling.

          `Prometheus` and `PrometheusAgent` objects select `PodMonitor` objects using label and namespace selectors.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: Specification of desired Pod selection for target discovery
              by Prometheus.
            properties:
              attachMetadata:
                description: |-
                  `attachMetadata` defines additional metadata which is added to the
                  discovered targets.

                  It requires Prometheus >= v2.35.0.
                properties:
                  node:
                    description: |-
                      When set to true, Prometheus attaches node metadata to the discovered
                      targets.

                      The Prometheus service account must have the `list` and `watch`
                      permissions on the `Nodes` objects.
                    type: boolean
                type: object
              bodySizeLimit:
                description: |-
                  When defined, bodySizeLimit specifies a job level limit on the size
                  of uncompressed response body that will be accepted by Prometheus.

                  It requires Prometheus >= v2.28.0.
                pattern: (^0|([0-9]*[.])?[0-9]+((K|M|G|T|E|P)i?)?B)$
                type: string
              convertClassicHistogramsToNHCB:
                description: |-
                  Whether to convert all scraped classic histograms into a native histogram with custom buckets.
                  It requires Prometheus >= v3.0.0.
                type: boolean
              fallbackScrapeProtocol:
                description: |-
                  The protocol to use if a scrape returns blank, unparseable, or otherwise invalid Content-Type.

                  It requires Prometheus >= v3.0.0.
                enum:
                - PrometheusProto
                - OpenMetricsText0.0.1
                - OpenMetricsText1.0.0
                - PrometheusText0.0.4
                - PrometheusText1.0.0
                type: string
              jobLabel:
                description: |-
                  The label to use to retrieve the job name from.
                  `jobLabel` selects the label from the associated Kubernetes `Pod`
                  object which will be used as the `job` label for all metrics.

                  For example if `jobLabel` is set to `foo` and the Kubernetes `Pod`
                  object is labeled with `foo: bar`, then Prometheus adds the `job="bar"`
                  label to all ingested metrics.

                  If the value of this field is empty, the `job` label of the metrics
                  defaults to the namespace and name of the PodMonitor object (e.g. `<namespace>/<name>`).
                type: string
              keepDroppedTargets:
                description: |-
                  Per-scrape limit on the number of targets dropped by relabeling
                  that will be kept in memory. 0 means no limit.

                  It requires Prometheus >= v2.47.0.
                format: int64
                type: integer
              labelLimit:
                description: |-
                  Per-scrape limit on number of labels that will be accepted for a sample.

                  It requires Prometheus >= v2.27.0.
                format: int64
                type: integer
              labelNameLengthLimit:
                description: |-
                  Per-scrape limit on length of labels name that will be accepted for a sample.

                  It requires Prometheus >= v2.27.0.
                format: int64
                type: integer
              labelValueLengthLimit:
                description: |-
                  Per-scrape limit on length of labels value that will be accepted for a sample.

                  It requires Prometheus >= v2.27.0.
                format: int64
                type: integer
              namespaceSelector:
                description: |-
                  `namespaceSelector` defines in which namespace(s) Prometheus should discover the pods.
                  By default, the pods are discovered in the same namespace as the `PodMonitor` object but it is possible to select pods across different/all namespaces.
                properties:
                  any:
                    description: |-
                      Boolean describing whether all namespaces are selected in contrast to a
                      list restricting them.
                    type: boolean
                  matchNames:
                    description: List of namespace names to select from.
                    items:
                      type: string
                    type: array
                type: object
              nativeHistogramBucketLimit:
                description: |-
                  If there are more than this many buckets in a native histogram,
                  buckets will be merged to stay within the limit.
                  It requires Prometheus >= v2.45.0.
                format: int64
                type: integer
              nativeHistogramMinBucketFactor:
                anyOf:
                - type: integer
                - type: string
                description: |-
                  If the growth factor of one bucket to the next is smaller than this,
                  buckets will be merged to increase the factor sufficiently.
                  It requires Prometheus >= v2.50.0.
                pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                x-kubernetes-int-or-string: true
              podMetricsEndpoints:
                description: Defines how to scrape metrics from the selected pods.
                items:
                  description: |-
                    PodMetricsEndpoint defines an endpoint serving Prometheus metrics to be scraped by
                    Prometheus.
                  properties:
                    authorization:
                      description: |-
                        `authorization` configures the Authorization header credentials to use when
                        scraping the target.

                        Cannot be set at the same time as `basicAuth`, or `oauth2`.
                      properties:
                        credentials:
                          description: Selects a key of a Secret in the namespace
                            that contains the credentials for authentication.
                          properties:
                            key:
                              description: The key of the secret to select from.  Must
                                be a valid secret key.
                              type: string
                            name:
                              default: ""
                              description: |-
                                Name of the referent.
                                This field is effectively required, but due to backwards compatibility is
                                allowed to be empty. Instances of this type with an empty value here are
                                almost certainly wrong.
                                More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                              type: string
                            optional:
                              description: Specify whether the Secret or its key must
                                be defined
                              type: boolean
                          required:
                          - key
                          type: object
                          x-kubernetes-map-type: atomic
                        type:
                          description: |-
                            Defines the authentication type. The value is case-insensitive.

                            "Basic" is not a supported value.

                            Default: "Bearer"
                          type: string
                      type: object
                    basicAuth:
                      description: |-
                        `basicAuth` configures the Basic Authentication credentials to use when
                        scraping the target.

                        Cannot be set at the same time as `authorization`, or `oauth2`.
                      properties:
                        password:
                          description: |-
                            `password` specifies a key of a Secret containing the password for
                            authentication.
                          properties:
                            key:
                              description: The key of the secret to select from.  Must
                                be a valid secret key.
                              type: string
                            name:
                              default: ""
                              description: |-
                                Name of the referent.
                                This field is effectively required, but due to backwards compatibility is
                                allowed to be empty. Instances of this type with an empty value here are
                                almost certainly wrong.
                                More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                              type: string
                            optional:
                              description: Specify whether the Secret or its key must
                                be defined
                              type: boolean
                          required:
                          - key
                          type: object
                          x-kubernetes-map-type: atomic
                        username:
                          description: |-
                            `username` specifies a key of a Secret containing the username for
                            authentication.
                          properties:
                            key:
                              description: The key of the secret to select from.  Must
                                be a valid secret key.
                              type: string
                            name:
                              default: ""
                              description: |-
                                Name of the referent.
                                This field is effectively required, but due to backwards compatibility is
                                allowed to be empty. Instances of this type with an empty value here are
                                almost certainly wrong.
                                More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                              type: string
                            optional:
                              description: Specify whether the Secret or its key must
                                be defined
                              type: boolean
                          required:
                          - key
                          type: object
                          x-kubernetes-map-type: atomic
                      type: object
                    bearerTokenSecret:
                      description: |-
                        `bearerTokenSecret` specifies a key of a Secret containing the bearer
                        token for scraping targets. The secret needs to be in the same namespace
                        as the PodMonitor object and readable by the Prometheus Operator.

                        Deprecated: use `authorization` instead.
                      properties:
                        key:
                          description: The key of the secret to select from.  Must
                            be a valid secret key.
                          type: string
                        name:
                          default: ""
                          description: |-
                            Name of the referent.
                            This field is effectively required, but due to backwards compatibility is
                            allowed to be empty. Instances of this type with an empty value here are
                            almost certainly wrong.
                            More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                          type: string
                        optional:
                          description: Specify whether the Secret or its key must
                            be defined
                          type: boolean
                      required:
                      - key
                      type: object
                      x-kubernetes-map-type: atomic
                    enableHttp2:
                      description: '`enableHttp2` can be used to disable HTTP2 when
                        scraping the target.'
                      type: boolean
                    filterRunning:
                      description: |-
                        When true, the pods which are not running (e.g. either in Failed or
                        Succeeded state) are dropped during the target discovery.

                        If unset, the filtering is enabled.

                        More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle/#pod-phase
                      type: boolean
                    followRedirects:
                      description: |-
                        `followRedirects` defines whether the scrape requests should follow HTTP
                        3xx redirects.
                      type: boolean
                    honorLabels:
                      description: |-
                        When true, `honorLabels` preserves the metric's labels when they collide
                        with the target's labels.
                      type: boolean
                    honorTimestamps:
                      description: |-
                        `honorTimestamps` controls whether Prometheus preserves the timestamps
                        when exposed by the target.
                      type: boolean
                    interval:
                      description: |-
                        Interval at which Prometheus scrapes the metrics from the target.

                        If empty, Prometheus uses the global scrape interval.
                      pattern: ^(0|(([0-9]+)y)?(([0-9]+)w)?(([0-9]+)d)?(([0-9]+)h)?(([0-9]+)m)?(([0-9]+)s)?(([0-9]+)ms)?)$
                      type: string
                    metricRelabelings:
                      description: |-
                        `metricRelabelings` configures the relabeling rules to apply to the
                        samples before ingestion.
                      items:
                        description: |-
                          RelabelConfig allows dynamic rewriting of the label set for targets, alerts,
                          scraped samples and remote write samples.

                          More info: https://prometheus.io/docs/prometheus/latest/configuration/configuration/#relabel_config
                        properties:
                          action:
                            default: replace
                            description: |-
                              Action to perform based on the regex matching.

                              `Uppercase` and `Lowercase` actions require Prometheus >= v2.36.0.
                              `DropEqual` and `KeepEqual` actions require Prometheus >= v2.41.0.

                              Default: "Replace"
                            enum:
                            - replace
                            - Replace
                            - keep
                            - Keep
                            - drop
                            - Drop
                            - hashmod
                            - HashMod
                            - labelmap
                            - LabelMap
                            - labeldrop
                            - LabelDrop
                            - labelkeep
                            - LabelKeep
                            - lowercase
                            - Lowercase
                            - uppercase
                            - Uppercase
                            - keepequal
                            - KeepEqual
                            - dropequal
                            - DropEqual
                            type: string
                          modulus:
                            description: |-
                              Modulus to take of the hash of the source label values.

                              Only applicable when the action is `HashMod`.
                            format: int64
                            type: integer
                          regex:
                            description: Regular expression against which the extracted
                              value is matched.
                            type: string
                          replacement:
                            description: |-
                              Replacement value against which a Replace action is performed if the
                              regular expression matches.

                              Regex capture groups are available.
                            type: string
                          separator:
                            description: Separator is the string between concatenated
                              SourceLabels.
                            type: string
                          sourceLabels:
                            description: |-
                              The source labels select values from existing labels. Their content is
                              concatenated using the configured Separator and matched against the
                              configured regular expression.
                            items:
                              description: |-
                                LabelName is a valid Prometheus label name which may only contain ASCII
                                letters, numbers, as well as underscores.
                              pattern: ^[a-zA-Z_][a-zA-Z0-9_]*$
                              type: string
                            type: array
                          targetLabel:
                            description: |-
                              Label to which the resulting string is written in a replacement.

                              It is mandatory for `Replace`, `HashMod`, `Lowercase`, `Uppercase`,
                              `KeepEqual` and `DropEqual` actions.

                              Regex capture groups are available.
                            type: string
                        type: object
                      type: array
                    noProxy:
                      description: |-
                        `noProxy` is a comma-separated string that can contain IPs, CIDR notation, domain names
                        that should be excluded from proxying. IP and domain names can
                        contain port numbers.

                        It requires Prometheus >= v2.43.0, Alertmanager >= v0.25.0 or Thanos >= v0.32.0.
                      type: string
                    oauth2:
                      description: |-
                        `oauth2` configures the OAuth2 settings to use when scraping the target.

                        It requires Prometheus >= 2.27.0.

                        Cannot be set at the same time as `authorization`, or `basicAuth`.
                      properties:
                        clientId:
                          description: |-
                            `clientId` specifies a key of a Secret or ConfigMap containing the
                            OAuth2 client's ID.
                          properties:
                            configMap:
                              description: ConfigMap containing data to use for the
                                targets.
                              properties:
                                key:
                                  description: The key to select.
                                  type: string
                                name:
                                  default: ""
                                  description: |-
                                    Name of the referent.
                                    This field is effectively required, but due to backwards compatibility is
                                    allowed to be empty. Instances of this type with an empty value here are
                                    almost certainly wrong.
                                    More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                                  type: string
                                optional:
                                  description: Specify whether the ConfigMap or its
                                    key must be defined
                                  type: boolean
                              required:
                              - key
                              type: object
                              x-kubernetes-map-type: atomic
                            secret:
                              description: Secret containing data to use for the targets.
                              properties:
                                key:
                                  description: The key of the secret to select from.  Must
                                    be a valid secret key.
                                  type: string
                                name:
                                  default: ""
                                  description: |-
                                    Name of the referent.
                                    This field is effectively required, but due to backwards compatibility is
                                    allowed to be empty. Instances of this type with an empty value here are
                                    almost certainly wrong.
                                    More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                                  type: string
                                optional:
                                  description: Specify whether the Secret or its key
                                    must be defined
                                  type: boolean
                              required:
                              - key
                              type: object
                              x-kubernetes-map-type: atomic
                          type: object
                        clientSecret:
                          description: |-
                            `clientSecret` specifies a key of a Secret containing the OAuth2
                            client's secret.
                          properties:
                            key:
                              description: The key of the secret to select from.  Must
                                be a valid secret key.
                              type: string
                            name:
                              default: ""
                              description: |-
                                Name of the referent.
                                This field is effectively required, but due to backwards compatibility is
                                allowed to be empty. Instances of this type with an empty value here are
                                almost certainly wrong.
                                More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                              type: string
                            optional:
                              description: Specify whether the Secret or its key must
                                be defined
                              type: boolean
                          required:
                          - key
                          type: object
                          x-kubernetes-map-type: atomic
                        endpointParams:
                          additionalProperties:
                            type: string
                          description: |-
                            `endpointParams` configures the HTTP parameters to append to the token
                            URL.
                          type: object
                        noProxy:
                          description: |-
                            `noProxy` is a comma-separated string that can contain IPs, CIDR notation, domain names
                            that should be excluded from proxying. IP and domain names can
                            contain port numbers.

                            It requires Prometheus >= v2.43.0, Alertmanager >= v0.25.0 or Thanos >= v0.32.0.
                          type: string
                        proxyConnectHeader:
                          additionalProperties:
                            items:
                              description: SecretKeySelector selects a key of a Secret.
                              properties:
                                key:
                                  description: The key of the secret to select from.  Must
                                    be a valid secret key.
                                  type: string
                                name:
                                  default: ""
                                  description: |-
                                    Name of the referent.
                                    This field is effectively required, but due to backwards compatibility is
                                    allowed to be empty. Instances of this type with an empty value here are
                                    almost certainly wrong.
                                    More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                                  type: string
                                optional:
                                  description: Specify whether the Secret or its key
                                    must be defined
                                  type: boolean
                              required:
                              - key
                              type: object
                              x-kubernetes-map-type: atomic
                            type: array
                          description: |-
                            ProxyConnectHeader optionally specifies headers to send to
                            proxies during CONNECT requests.

                            It requires Prometheus >= v2.43.0, Alertmanager >= v0.25.0 or Thanos >= v0.32.0.
                          type: object
                          x-kubernetes-map-type: atomic
                        proxyFromEnvironment:
                          description: |-
                            Whether to use the proxy configuration defined by environment variables (HTTP_PROXY, HTTPS_PROXY, and NO_PROXY).

                            It requires Prometheus >= v2.43.0, Alertmanager >= v0.25.0 or Thanos >= v0.32.0.
                          type: boolean
                        proxyUrl:
                          description: '`proxyURL` defines the HTTP proxy server to
                            use.'
                          pattern: ^(http|https|socks5)://.+$
                          type: string
                        scopes:
                          description: '`scopes` defines the OAuth2 scopes used for
                            the token request.'
                          items:
                            type: string
                          type: array
                        tlsConfig:
                          description: |-
                            TLS configuration to use when connecting to the OAuth2 server.
                            It requires Prometheus >= v2.43.0.
                          properties:
                            ca:
                              description: Certificate authority used when verifying
                                server certificates.
                              properties:
                                configMap:
                                  description: ConfigMap containing data to use for
                                    the targets.
                                  properties:
                                    key:
                                      description: The key to select.
                                      type: string
                                    name:
                                      default: ""
                                      description: |-
                                        Name of the referent.
                                        This field is effectively required, but due to backwards compatibility is
                                        allowed to be empty. Instances of this type with an empty value here are
                                        almost certainly wrong.
                                        More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                                      type: string
                                    optional:
                                      description: Specify whether the ConfigMap or
                                        its key must be defined
                                      type: boolean
                                  required:
                                  - key
                                  type: object
                                  x-kubernetes-map-type: atomic
                                secret:
                                  description: Secret containing data to use for the
                                    targets.
                                  properties:
                                    key:
                                      description: The key of the secret to select
                                        from.  Must be a valid secret key.
                                      type: string
                                    name:
                                      default: ""
                                      description: |-
                                        Name of the referent.
                                        This field is effectively required, but due to backwards compatibility is
                                        allowed to be empty. Instances of this type with an empty value here are
                                        almost certainly wrong.
                                        More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                                      type: string
                                    optional:
                                      description: Specify whether the Secret or its
                                        key must be defined
                                      type: boolean
                                  required:
                                  - key
                                  type: object
                                  x-kubernetes-map-type: atomic
                              type: object
                            cert:
                              description: Client certificate to present when doing
                                client-authentication.
                              properties:
                                configMap:
                                  description: ConfigMap containing data to use for
                                    the targets.
                                  properties:
                                    key:
                                      description: The key to select.
                                      type: string
                                    name:
                                      default: ""
                                      description: |-
                                        Name of the referent.
                                        This field is effectively required, but due to backwards compatibility is
                                        allowed to be empty. Instances of this type with an empty value here are
                                        almost certainly wrong.
                                        More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                                      type: string
                                    optional:
                                      description: Specify whether the ConfigMap or
                                        its key must be defined
                                      type: boolean
                                  required:
                                  - key
                                  type: object
                                  x-kubernetes-map-type: atomic
                                secret:
                                  description: Secret containing data to use for the
                                    targets.
                                  properties:
                                    key:
                                      description: The key of the secret to select
                                        from.  Must be a valid secret key.
                                      type: string
                                    name:
                                      default: ""
                                      description: |-
                                        Name of the referent.
                                        This field is effectively required, but due to backwards compatibility is
                                        allowed to be empty. Instances of this type with an empty value here are
                                        almost certainly wrong.
                                        More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                                      type: string
                                    optional:
                                      description: Specify whether the Secret or its
                                        key must be defined
                                      type: boolean
                                  required:
                                  - key
                                  type: object
                                  x-kubernetes-map-type: atomic
                              type: object
                            insecureSkipVerify:
                              description: Disable target certificate validation.
                              type: boolean
                            keySecret:
                              description: Secret containing the client key file for
                                the targets.
                              properties:
                                key:
                                  description: The key of the secret to select from.  Must
                                    be a valid secret key.
                                  type: string
                                name:
                                  default: ""
                                  description: |-
                                    Name of the referent.
                                    This field is effectively required, but due to backwards compatibility is
                                    allowed to be empty. Instances of this type with an empty value here are
                                    almost certainly wrong.
                                    More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                                  type: string
                                optional:
                                  description: Specify whether the Secret or its key
                                    must be defined
                                  type: boolean
                              required:
                              - key
                              type: object
                              x-kubernetes-map-type: atomic
                            maxVersion:
                              description: |-
                                Maximum acceptable TLS version.

                                It requires Prometheus >= v2.41.0 or Thanos >= v0.31.0.
                              enum:
                              - TLS10
                              - TLS11
                              - TLS12
                              - TLS13
                              type: string
                            minVersion:
                              description: |-
                                Minimum acceptable TLS version.

                                It requires Prometheus >= v2.35.0 or Thanos >= v0.28.0.
                              enum:
                              - TLS10
                              - TLS11
                              - TLS12
                              - TLS13
                              type: string
                            serverName:
                              description: Used to verify the hostname for the targets.
                              type: string
                          type: object
                        tokenUrl:
                          description: '`tokenURL` configures the URL to fetch the
                            token from.'
                          minLength: 1
                          type: string
                      required:
                      - clientId
                      - clientSecret
                      - tokenUrl
                      type: object
                    params:
                      additionalProperties:
                        items:
                          type: string
                        type: array
                      description: '`params` define optional HTTP URL parameters.'
                      type: object
                    path:
                      description: |-
                        HTTP path from which to scrape for metrics.

                        If empty, Prometheus uses the default value (e.g. `/metrics`).
                      type: string
                    port:
                      description: |-
                        The `Pod` port name which exposes the endpoint.

                        It takes precedence over the `portNumber` and `targetPort` fields.
                      type: string
                    portNumber:
                      description: The `Pod` port number which exposes the endpoint.
                      format: int32
                      maximum: 65535
                      minimum: 1
                      type: integer
                    proxyConnectHeader:
                      additionalProperties:
                        items:
                          description: SecretKeySelector selects a key of a Secret.
                          properties:
                            key:
                              description: The key of the secret to select from.  Must
                                be a valid secret key.
                              type: string
                            name:
                              default: ""
                              description: |-
                                Name of the referent.
                                This field is effectively required, but due to backwards compatibility is
                                allowed to be empty. Instances of this type with an empty value here are
                                almost certainly wrong.
                                More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                              type: string
                            optional:
                              description: Specify whether the Secret or its key must
                                be defined
                              type: boolean
                          required:
                          - key
                          type: object
                          x-kubernetes-map-type: atomic
                        type: array
                      description: |-
                        ProxyConnectHeader optionally specifies headers to send to
                        proxies during CONNECT requests.

                        It requires Prometheus >= v2.43.0, Alertmanager >= v0.25.0 or Thanos >= v0.32.0.
                      type: object
                      x-kubernetes-map-type: atomic
                    proxyFromEnvironment:
                      description: |-
                        Whether to use the proxy configuration defined by environment variables (HTTP_PROXY, HTTPS_PROXY, and NO_PROXY).

                        It requires Prometheus >= v2.43.0, Alertmanager >= v0.25.0 or Thanos >= v0.32.0.
                      type: boolean
                    proxyUrl:
                      description: '`proxyURL` defines the HTTP proxy server to use.'
                      pattern: ^(http|https|socks5)://.+$
                      type: string
                    relabelings:
                      description: |-
                        `relabelings` configures the relabeling rules to apply the target's
                        metadata labels.

                        The Operator automatically adds relabelings for a few standard Kubernetes fields.

                        The original scrape job's name is available via the `__tmp_prometheus_job_name` label.

                        More info: https://prometheus.io/docs/prometheus/latest/configuration/configuration/#relabel_config
                      items:
                        description: |-
                          RelabelConfig allows dynamic rewriting of the label set for targets, alerts,
                          scraped samples and remote write samples.

                          More info: https://prometheus.io/docs/prometheus/latest/configuration/configuration/#relabel_config
                        properties:
                          action:
                            default: replace
                            description: |-
                              Action to perform based on the regex matching.

                              `Uppercase` and `Lowercase` actions require Prometheus >= v2.36.0.
                              `DropEqual` and `KeepEqual` actions require Prometheus >= v2.41.0.

                              Default: "Replace"
                            enum:
                            - replace
                            - Replace
                            - keep
                            - Keep
                            - drop
                            - Drop
                            - hashmod
                            - HashMod
                            - labelmap
                            - LabelMap
                            - labeldrop
                            - LabelDrop
                            - labelkeep
                            - LabelKeep
                            - lowercase
                            - Lowercase
                            - uppercase
                            - Uppercase
                            - keepequal
                            - KeepEqual
                            - dropequal
                            - DropEqual
                            type: string
                          modulus:
                            description: |-
                              Modulus to take of the hash of the source label values.

                              Only applicable when the action is `HashMod`.
                            format: int64
                            type: integer
                          regex:
                            description: Regular expression against which the extracted
                              value is matched.
                            type: string
                          replacement:
                            description: |-
                              Replacement value against which a Replace action is performed if the
                              regular expression matches.

                              Regex capture groups are available.
                            type: string
                          separator:
                            description: Separator is the string between concatenated
                              SourceLabels.
                            type: string
                          sourceLabels:
                            description: |-
                              The source labels select values from existing labels. Their content is
                              concatenated using the configured Separator and matched against the
                              configured regular expression.
                            items:
                              description: |-
                                LabelName is a valid Prometheus label name which may only contain ASCII
                                letters, numbers, as well as underscores.
                              pattern: ^[a-zA-Z_][a-zA-Z0-9_]*$
                              type: string
                            type: array
                          targetLabel:
                            description: |-
                              Label to which the resulting string is written in a replacement.

                              It is mandatory for `Replace`, `HashMod`, `Lowercase`, `Uppercase`,
                              `KeepEqual` and `DropEqual` actions.

                              Regex capture groups are available.
                            type: string
                        type: object
                      type: array
                    scheme:
                      description: |-
                        HTTP scheme to use for scraping.

                        `http` and `https` are the expected values unless you rewrite the
                        `__scheme__` label via relabeling.

                        If empty, Prometheus uses the default value `http`.
                      enum:
                      - http
                      - https
                      type: string
                    scrapeTimeout:
                      description: |-
                        Timeout after which Prometheus considers the scrape to be failed.

                        If empty, Prometheus uses the global scrape timeout unless it is less
                        than the target's scrape interval value in which the latter is used.
                        The value cannot be greater than the scrape interval otherwise the operator will reject the resource.
                      pattern: ^(0|(([0-9]+)y)?(([0-9]+)w)?(([0-9]+)d)?(([0-9]+)h)?(([0-9]+)m)?(([0-9]+)s)?(([0-9]+)ms)?)$
                      type: string
                    targetPort:
                      anyOf:
                      - type: integer
                      - type: string
                      description: |-
                        Name or number of the target port of the `Pod` object behind the Service, the
                        port must be specified with container port property.

                        Deprecated: use 'port' or 'portNumber' instead.
                      x-kubernetes-int-or-string: true
                    tlsConfig:
                      description: TLS configuration to use when scraping the target.
                      properties:
                        ca:
                          description: Certificate authority used when verifying server
                            certificates.
                          properties:
                            configMap:
                              description: ConfigMap containing data to use for the
                                targets.
                              properties:
                                key:
                                  description: The key to select.
                                  type: string
                                name:
                                  default: ""
                                  description: |-
                                    Name of the referent.
                                    This field is effectively required, but due to backwards compatibility is
                                    allowed to be empty. Instances of this type with an empty value here are
                                    almost certainly wrong.
                                    More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                                  type: string
                                optional:
                                  description: Specify whether the ConfigMap or its
                                    key must be defined
                                  type: boolean
                              required:
                              - key
                              type: object
                              x-kubernetes-map-type: atomic
                            secret:
                              description: Secret containing data to use for the targets.
                              properties:
                                key:
                                  description: The key of the secret to select from.  Must
                                    be a valid secret key.
                                  type: string
                                name:
                                  default: ""
                                  description: |-
                                    Name of the referent.
                                    This field is effectively required, but due to backwards compatibility is
                                    allowed to be empty. Instances of this type with an empty value here are
                                    almost certainly wrong.
                                    More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                                  type: string
                                optional:
                                  description: Specify whether the Secret or its key
                                    must be defined
                                  type: boolean
                              required:
                              - key
                              type: object
                              x-kubernetes-map-type: atomic
                          type: object
                        cert:
                          description: Client certificate to present when doing client-authentication.
                          properties:
                            configMap:
                              description: ConfigMap containing data to use for the
                                targets.
                              properties:
                                key:
                                  description: The key to select.
                                  type: string
                                name:
                                  default: ""
                                  description: |-
                                    Name of the referent.
                                    This field is effectively required, but due to backwards compatibility is
                                    allowed to be empty. Instances of this type with an empty value here are
                                    almost certainly wrong.
                                    More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                                  type: string
                                optional:
                                  description: Specify whether the ConfigMap or its
                                    key must be defined
                                  type: boolean
                              required:
                              - key
                              type: object
                              x-kubernetes-map-type: atomic
                            secret:
                              description: Secret containing data to use for the targets.
                              properties:
                                key:
                                  description: The key of the secret to select from.  Must
                                    be a valid secret key.
                                  type: string
                                name:
                                  default: ""
                                  description: |-
                                    Name of the referent.
                                    This field is effectively required, but due to backwards compatibility is
                                    allowed to be empty. Instances of this type with an empty value here are
                                    almost certainly wrong.
                                    More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                                  type: string
                                optional:
                                  description: Specify whether the Secret or its key
                                    must be defined
                                  type: boolean
                              required:
                              - key
                              type: object
                              x-kubernetes-map-type: atomic
                          type: object
                        insecureSkipVerify:
                          description: Disable target certificate validation.
                          type: boolean
                        keySecret:
                          description: Secret containing the client key file for the
                            targets.
                          properties:
                            key:
                              description: The key of the secret to select from.  Must
                                be a valid secret key.
                              type: string
                            name:
                              default: ""
                              description: |-
                                Name of the referent.
                                This field is effectively required, but due to backwards compatibility is
                                allowed to be empty. Instances of this type with an empty value here are
                                almost certainly wrong.
                                More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                              type: string
                            optional:
                              description: Specify whether the Secret or its key must
                                be defined
                              type: boolean
                          required:
                          - key
                          type: object
                          x-kubernetes-map-type: atomic
                        maxVersion:
                          description: |-
                            Maximum acceptable TLS version.

                            It requires Prometheus >= v2.41.0 or Thanos >= v0.31.0.
                          enum:
                          - TLS10
                          - TLS11
                          - TLS12
                          - TLS13
                          type: string
                        minVersion:
                          description: |-
                            Minimum acceptable TLS version.

                            It requires Prometheus >= v2.35.0 or Thanos >= v0.28.0.
                          enum:
                          - TLS10
                          - TLS11
                          - TLS12
                          - TLS13
                          type: string
                        serverName:
                          description: Used to verify the hostname for the targets.
                          type: string
                      type: object
                    trackTimestampsStaleness:
                      description: |-
                        `trackTimestampsStaleness` defines whether Prometheus tracks staleness of
                        the metrics that have an explicit timestamp present in scraped data.
                        Has no effect if `honorTimestamps` is false.

                        It requires Prometheus >= v2.48.0.
                      type: boolean
                  type: object
                type: array
              podTargetLabels:
                description: |-
                  `podTargetLabels` defines the labels which are transferred from the
                  associated Kubernetes `Pod` object onto the ingested metrics.
                items:
                  type: string
                type: array
              sampleLimit:
                description: |-
                  `sampleLimit` defines a per-scrape limit on the number of scraped samples
                  that will be accepted.
                format: int64
                type: integer
              scrapeClass:
                description: The scrape class to apply.
                minLength: 1
                type: string
              scrapeClassicHistograms:
                description: |-
                  Whether to scrape a classic histogram that is also exposed as a native histogram.
                  It requires Prometheus >= v2.45.0.
                type: boolean
              scrapeProtocols:
                description: |-
                  `scrapeProtocols` defines the protocols to negotiate during a scrape. It tells clients the
                  protocols supported by Prometheus in order of preference (from most to least preferred).

                  If unset, Prometheus uses its default value.

                  It requires Prometheus >= v2.49.0.
                items:
                  description: |-
                    ScrapeProtocol represents a protocol used by Prometheus for scraping metrics.
                    Supported values are:
                    * `OpenMetricsText0.0.1`
                    * `OpenMetricsText1.0.0`
                    * `PrometheusProto`
                    * `PrometheusText0.0.4`
                    * `PrometheusText1.0.0`
                  enum:
                  - PrometheusProto
                  - OpenMetricsText0.0.1
                  - OpenMetricsText1.0.0
                  - PrometheusText0.0.4
                  - PrometheusText1.0.0
                  type: string
                type: array
                x-kubernetes-list-type: set
              selector:
                description: Label selector to select the Kubernetes `Pod` objects
                  to scrape metrics from.
                properties:
                  matchExpressions:
                    description: matchExpressions is a list of label selector requirements.
                      The requirements are ANDed.
                    items:
                      description: |-
                        A label selector requirement is a selector that contains values, a key, and an operator that
                        relates the key and values.
                      properties:
                        key:
                          description: key is the label key that the selector applies
                            to.
                          type: string
                        operator:
                          description: |-
                            operator represents a key's relationship to a set of values.
                            Valid operators are In, NotIn, Exists and DoesNotExist.
                          type: string
                        values:
                          description: |-
                            values is an array of string values. If the operator is In or NotIn,
                            the values array must be non-empty. If the operator is Exists or DoesNotExist,
                            the values array must be empty. This array is replaced during a strategic
                            merge patch.
                          items:
                            type: string
                          type: array
                          x-kubernetes-list-type: atomic
                      required:
                      - key
                      - operator
                      type: object
                    type: array
                    x-kubernetes-list-type: atomic
                  matchLabels:
                    additionalProperties:
                      type: string
                    description: |-
                      matchLabels is a map of {key,value} pairs. A single {key,value} in the matchLabels
                      map is equivalent to an element of matchExpressions, whose key field is "key", the
                      operator is "In", and the values array contains only "value". The requirements are ANDed.
                    type: object
                type: object
                x-kubernetes-map-type: atomic
              selectorMechanism:
                description: |-
                  Mechanism used to select the endpoints to scrape.
                  By default, the selection process relies on relabel configurations to filter the discovered targets.
                  Alternatively, you can opt in for role selectors, which may offer better efficiency in large clusters.
                  Which strategy is best for your use case needs to be carefully evaluated.

                  It requires Prometheus >= v2.17.0.
                enum:
                - RelabelConfig
                - RoleSelector
                type: string
              targetLimit:
                description: |-
                  `targetLimit` defines a limit on the number of scraped targets that will
                  be accepted.
                format: int64
                type: integer
            required:
            - selector
            type: object
        required:
        - spec
        type: object
    served: true
    storage: true
{{- end -}}
