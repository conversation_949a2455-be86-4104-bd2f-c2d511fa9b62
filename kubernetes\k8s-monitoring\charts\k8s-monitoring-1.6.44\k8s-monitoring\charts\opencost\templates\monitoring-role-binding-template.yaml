{{- if (.Values.opencost.prometheus.createMonitoringResourceReaderRoleBinding) }}
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  namespace: {{ include "opencost.namespace" . }}
  name: {{ include "opencost.fullname" . }}-reader
subjects:
- kind: ServiceAccount
  name: {{ .Values.opencost.prometheus.monitoringServiceAccountName | quote }}
  namespace: {{ .Values.opencost.prometheus.monitoringServiceAccountNamespace | quote }}
roleRef:
  kind: Role
  name: {{ include "opencost.fullname" . }}-reader
  apiGroup: rbac.authorization.k8s.io
{{- end -}}
