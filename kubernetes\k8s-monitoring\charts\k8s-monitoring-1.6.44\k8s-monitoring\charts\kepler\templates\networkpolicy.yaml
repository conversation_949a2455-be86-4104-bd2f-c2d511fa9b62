{{- if .Values.networkPolicy.enabled }}
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: {{ include "kepler.fullname" . }}
  namespace: {{ .Release.Namespace }}
  labels: 
    {{- include "kepler.labels" . | nindent 4 }}
  {{- with .Values.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  {{- with .Values.networkPolicy.ingress }}
  ingress:
      {{- toYaml . | nindent 4 }}
  {{- end }}
  {{- with .Values.networkPolicy.egress }}
    egress:
      {{- toYaml . | nindent 4 }}
  {{- end }}
  podSelector:
    matchLabels:
      {{- include "kepler.selectorLabels" . | nindent 6 }}
  policyTypes:
  - Egress
  - Ingress
{{- end }}
