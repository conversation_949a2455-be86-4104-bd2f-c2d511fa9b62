{{- if .Values.rbac.create -}}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ include "kepler.fullname" . }}-clusterrole
rules:
  - apiGroups: [""]
    resources:
      - nodes/metrics # access /metrics/resource
      - nodes/proxy
      - nodes/stats
      - pods
    verbs:
      - get
      - watch
      - list

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: {{ include "kepler.fullname" . }}-clusterrole-binding
roleRef:
  kind: ClusterRole
  name: {{ include "kepler.fullname" . }}-clusterrole
  apiGroup: "rbac.authorization.k8s.io"
subjects:
  - kind: ServiceAccount
    name: {{ include "kepler.serviceAccountName" . }}
    namespace: {{ .Release.Namespace }}
{{- end }}
