{{- if and .Values.customResourceState.enabled .Values.customResourceState.create }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ template "kube-state-metrics.crsConfigMapName" . }}
  namespace: {{ template "kube-state-metrics.namespace" . }}
  labels:
    {{- include "kube-state-metrics.labels" . | indent 4 }}
  {{- if .Values.annotations }}
  annotations:
    {{ toYaml .Values.annotations | nindent 4 }}
  {{- end }}
data:
  {{ .Values.customResourceState.key }}: |
    {{- toYaml .Values.customResourceState.config | nindent 4 }}
{{- end }}
