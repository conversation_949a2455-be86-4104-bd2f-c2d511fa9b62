{{- if and (.Capabilities.APIVersions.Has "autoscaling.k8s.io/v1") (.Values.verticalPodAutoscaler.enabled) }}
apiVersion: autoscaling.k8s.io/v1
kind: VerticalPodAutoscaler
metadata:
  name: {{ template "kube-state-metrics.fullname" . }}
  namespace: {{ template "kube-state-metrics.namespace" . }}
  labels:
    {{- include "kube-state-metrics.labels" . | indent 4 }}
spec:
  {{- with .Values.verticalPodAutoscaler.recommenders }}
  recommenders:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  resourcePolicy:
    containerPolicies:
    - containerName: {{ template "kube-state-metrics.name" . }}
      {{- with .Values.verticalPodAutoscaler.controlledResources }}
      controlledResources:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- if .Values.verticalPodAutoscaler.controlledValues }}
      controlledValues: {{ .Values.verticalPodAutoscaler.controlledValues }}
      {{- end }}
      {{- if .Values.verticalPodAutoscaler.maxAllowed }}
      maxAllowed:
        {{ toYaml .Values.verticalPodAutoscaler.maxAllowed | nindent 8 }}
      {{- end }}
      {{- if .Values.verticalPodAutoscaler.minAllowed }}
      minAllowed:
        {{ toYaml .Values.verticalPodAutoscaler.minAllowed | nindent 8 }}
      {{- end }}
  targetRef:
    apiVersion: apps/v1
    {{- if .Values.autosharding.enabled }}
    kind: StatefulSet
    {{- else }}
    kind: Deployment
    {{- end }}
    name:  {{ template "kube-state-metrics.fullname" . }}
  {{- with .Values.verticalPodAutoscaler.updatePolicy }}
  updatePolicy:
    {{- toYaml . | nindent 4 }}
  {{- end }}
{{- end }}
