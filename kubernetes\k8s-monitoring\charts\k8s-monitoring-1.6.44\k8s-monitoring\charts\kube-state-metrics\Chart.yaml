annotations:
  artifacthub.io/license: Apache-2.0
  artifacthub.io/links: |
    - name: Chart Source
      url: https://github.com/prometheus-community/helm-charts
apiVersion: v2
appVersion: 2.16.0
description: Install kube-state-metrics to generate and expose cluster-level metrics
home: https://github.com/kubernetes/kube-state-metrics/
keywords:
- metric
- monitoring
- prometheus
- kubernetes
maintainers:
- email: <EMAIL>
  name: tariq1890
  url: https://github.com/tariq1890
- email: <EMAIL>
  name: mrueg
  url: https://github.com/mrueg
- email: <EMAIL>
  name: dotdc
  url: https://github.com/dotdc
name: kube-state-metrics
sources:
- https://github.com/kubernetes/kube-state-metrics/
type: application
version: 6.1.0
