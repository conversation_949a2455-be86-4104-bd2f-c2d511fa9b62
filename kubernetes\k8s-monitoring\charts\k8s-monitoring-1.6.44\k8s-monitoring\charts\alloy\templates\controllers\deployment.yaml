{{- if eq .Values.controller.type "deployment" }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "alloy.fullname" . }}
  namespace: {{ include "alloy.namespace" . }}
  labels:
    {{- include "alloy.labels" . | nindent 4 }}
  {{- with .Values.controller.extraAnnotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  {{- if (and (not .Values.controller.autoscaling.enabled) (not .Values.controller.autoscaling.horizontal.enabled)) }}
  replicas: {{ .Values.controller.replicas }}
  {{- end }}
  {{- if ge (int .Capabilities.KubeVersion.Minor) 22 }}
  minReadySeconds: {{ .Values.controller.minReadySeconds }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "alloy.selectorLabels" . | nindent 6 }}
  template:
    {{- include "alloy.pod-template" . | nindent 4 }}
  {{- with .Values.controller.updateStrategy }}
  strategy:
    {{- toYaml . | nindent 4 }}
  {{- end }}
{{- end }}
