{{- define "alloy.pod-template" -}}
{{- $values := (mustMergeOverwrite .Values.alloy (or .Values.agent dict)) -}}
metadata:
  annotations:
    kubectl.kubernetes.io/default-container: alloy
    {{- if and $values.configMap.create $values.configMap.content }}
    checksum/config: {{ (tpl  $values.configMap.content .) | sha256sum | trunc 63 }}
    {{- end }}
    {{- with .Values.controller.podAnnotations }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
  labels:
    {{- include "alloy.selectorLabels" . | nindent 4 }}
    {{- with .Values.controller.podLabels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
spec:
  {{- with .Values.global.podSecurityContext }}
  securityContext:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  serviceAccountName: {{ include "alloy.serviceAccountName" . }}
  {{- if or .Values.global.image.pullSecrets .Values.image.pullSecrets }}
  imagePullSecrets:
    {{- if .Values.global.image.pullSecrets }}
    {{- toYaml .Values.global.image.pullSecrets | nindent 4 }}
    {{- else }}
    {{- toYaml .Values.image.pullSecrets | nindent 4 }}
    {{- end }}
  {{- end }}
  {{- if .Values.controller.initContainers }}
  initContainers:
    {{- with .Values.controller.initContainers }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
  {{- end }}
  containers:
    {{- include "alloy.container" . | nindent 4 }}
    {{- include "alloy.watch-container" . | nindent 4 }}
    {{- with .Values.controller.extraContainers }}
    {{- toYaml . | nindent 4 }}
    {{- end}}
  {{- if .Values.controller.priorityClassName }}
  priorityClassName: {{ .Values.controller.priorityClassName }}
  {{- end }}
  {{- if .Values.controller.hostNetwork }}
  hostNetwork: {{ .Values.controller.hostNetwork }}
  {{- end }}
  {{- if .Values.controller.hostPID }}
  hostPID: {{ .Values.controller.hostPID }}
  {{- end }}
  dnsPolicy: {{ .Values.controller.dnsPolicy }}
  {{- with .Values.controller.affinity }}
  affinity:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  {{- if .Values.controller.terminationGracePeriodSeconds }}
  terminationGracePeriodSeconds: {{ .Values.controller.terminationGracePeriodSeconds | int }}
  {{- end }}
  {{- with .Values.controller.nodeSelector }}
  nodeSelector:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  {{- with .Values.controller.tolerations }}
  tolerations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  {{- with .Values.controller.topologySpreadConstraints }}
  topologySpreadConstraints:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  volumes:
    - name: config
      configMap:
        name: {{ include "alloy.config-map.name" . }}
    {{- if $values.mounts.varlog }}
    - name: varlog
      hostPath:
        path: /var/log
    {{- end }}
    {{- if $values.mounts.dockercontainers }}
    - name: dockercontainers
      hostPath:
        path: /var/lib/docker/containers
    {{- end }}
    {{- if .Values.controller.volumes.extra }}
    {{- toYaml .Values.controller.volumes.extra | nindent 4 }}
    {{- end }}
  {{- if $values.hostAliases }}
  hostAliases:
    {{- toYaml $values.hostAliases | nindent 4 }}
  {{- end }}
{{- end }}
