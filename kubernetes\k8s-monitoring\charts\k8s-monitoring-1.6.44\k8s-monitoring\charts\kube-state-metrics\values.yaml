# Default values for kube-state-metrics.
prometheusScrape: true
image:
  registry: registry.k8s.io
  repository: kube-state-metrics/kube-state-metrics
  # If unset use v + .Charts.appVersion
  tag: ""
  sha: ""
  pullPolicy: IfNotPresent

imagePullSecrets: []
# - name: "image-pull-secret"

global:
  # To help compatibility with other charts which use global.imagePullSecrets.
  # Allow either an array of {name: pullSecret} maps (k8s-style), or an array of strings (more common helm-style).
  # global:
  #   imagePullSecrets:
  #   - name: pullSecret1
  #   - name: pullSecret2
  # or
  # global:
  #   imagePullSecrets:
  #   - pullSecret1
  #   - pullSecret2
  imagePullSecrets: []
  #
  # Allow parent charts to override registry hostname
  imageRegistry: ""

# If set to true, this will deploy kube-state-metrics as a StatefulSet and the data
# will be automatically sharded across <.Values.replicas> pods using the built-in
# autodiscovery feature: https://github.com/kubernetes/kube-state-metrics#automated-sharding
# This is an experimental feature and there are no stability guarantees.
autosharding:
  enabled: false

replicas: 1

# Change the deployment strategy when autosharding is disabled.
# ref: https://kubernetes.io/docs/concepts/workloads/controllers/deployment/#strategy
# The default is "RollingUpdate" as per Kubernetes defaults.
# During a release, 'RollingUpdate' can lead to two running instances for a short period of time while 'Recreate' can create a small gap in data.
# updateStrategy: Recreate

# Number of old history to retain to allow rollback
# Default Kubernetes value is set to 10
revisionHistoryLimit: 10

# List of additional cli arguments to configure kube-state-metrics
# for example: --enable-gzip-encoding, --log-file, etc.
# all the possible args can be found here: https://github.com/kubernetes/kube-state-metrics/blob/master/docs/cli-arguments.md
extraArgs: []

# If false then the user will opt out of automounting API credentials.
automountServiceAccountToken: true

service:
  port: 8080
  # Default to clusterIP for backward compatibility
  type: ClusterIP
  ipDualStack:
    enabled: false
    ipFamilies: ["IPv6", "IPv4"]
    ipFamilyPolicy: "PreferDualStack"
  nodePort: 0
  loadBalancerIP: ""
  # Only allow access to the loadBalancerIP from these IPs
  loadBalancerSourceRanges: []
  clusterIP: ""
  annotations: {}

## Additional labels to add to all resources
customLabels: {}
  # app: kube-state-metrics

## Override selector labels
selectorOverride: {}

## set to true to add the release label so scraping of the servicemonitor with kube-prometheus-stack works out of the box
releaseLabel: false

hostNetwork: false

rbac:
  # If true, create & use RBAC resources
  create: true

  # Set to a rolename to use existing role - skipping role creating - but still doing serviceaccount and rolebinding to it, rolename set here.
  # useExistingRole: your-existing-role

  # If set to false - Run without Cluteradmin privs needed - ONLY works if namespace is also set (if useExistingRole is set this name is used as ClusterRole or Role to bind to)
  useClusterRole: true

  # Add permissions for CustomResources' apiGroups in Role/ClusterRole. Should be used in conjunction with Custom Resource State Metrics configuration
  # Example:
  # - apiGroups: ["monitoring.coreos.com"]
  #   resources: ["prometheuses"]
  #   verbs: ["list", "watch"]
  extraRules: []

# Configure kube-rbac-proxy. When enabled, creates one kube-rbac-proxy container per exposed HTTP endpoint (metrics and telemetry if enabled).
# The requests are served through the same service but requests are then HTTPS.
kubeRBACProxy:
  enabled: false
  image:
    registry: quay.io
    repository: brancz/kube-rbac-proxy
    tag: v0.19.1
    sha: ""
    pullPolicy: IfNotPresent

  # List of additional cli arguments to configure kube-rbac-prxy
  # for example: --tls-cipher-suites, --log-file, etc.
  # all the possible args can be found here: https://github.com/brancz/kube-rbac-proxy#usage
  extraArgs: []

  ## Specify security settings for a Container
  ## Allows overrides and additional options compared to (Pod) securityContext
  ## Ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-container
  containerSecurityContext:
    readOnlyRootFilesystem: true
    allowPrivilegeEscalation: false
    capabilities:
      drop:
      - ALL

  resources: {}
    # We usually recommend not to specify default resources and to leave this as a conscious
    # choice for the user. This also increases chances charts run on environments with little
    # resources, such as Minikube. If you do want to specify resources, uncomment the following
    # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
    # limits:
    #  cpu: 100m
    #  memory: 64Mi
    # requests:
    #  cpu: 10m
    #  memory: 32Mi

  ## volumeMounts enables mounting custom volumes in rbac-proxy containers
  ## Useful for TLS certificates and keys
  volumeMounts: []
    # - mountPath: /etc/tls
    #   name: kube-rbac-proxy-tls
    #   readOnly: true

serviceAccount:
  # Specifies whether a ServiceAccount should be created, require rbac true
  create: true
  # The name of the ServiceAccount to use.
  # If not set and create is true, a name is generated using the fullname template
  name:
  # Reference to one or more secrets to be used when pulling images
  # ref: https://kubernetes.io/docs/tasks/configure-pod-container/pull-image-private-registry/
  imagePullSecrets: []
  # ServiceAccount annotations.
  # Use case: AWS EKS IAM roles for service accounts
  # ref: https://docs.aws.amazon.com/eks/latest/userguide/specify-service-account-role.html
  annotations: {}
  # If false then the user will opt out of automounting API credentials.
  automountServiceAccountToken: true

# Additional Environment variables
env: []
  # - name: GOMAXPROCS
  #   valueFrom:
  #     resourceFieldRef:
  #       resource: limits.cpu

prometheus:
  monitor:
    enabled: false
    annotations: {}
    additionalLabels: {}
    namespace: ""
    namespaceSelector: []
    jobLabel: ""
    targetLabels: []
    podTargetLabels: []
    ## SampleLimit defines per-scrape limit on number of scraped samples that will be accepted.
    ##
    sampleLimit: 0

    ## TargetLimit defines a limit on the number of scraped targets that will be accepted.
    ##
    targetLimit: 0

    ## Per-scrape limit on number of labels that will be accepted for a sample. Only valid in Prometheus versions 2.27.0 and newer.
    ##
    labelLimit: 0

    ## Per-scrape limit on length of labels name that will be accepted for a sample. Only valid in Prometheus versions 2.27.0 and newer.
    ##
    labelNameLengthLimit: 0

    ## Per-scrape limit on length of labels value that will be accepted for a sample. Only valid in Prometheus versions 2.27.0 and newer.
    ##
    labelValueLengthLimit: 0
    selectorOverride: {}

    ## kube-state-metrics endpoint
    http:
      interval: ""
      scrapeTimeout: ""
      proxyUrl: ""
      ## Whether to enable HTTP2 for servicemonitor
      enableHttp2: false
      honorLabels: false
      metricRelabelings: []
      relabelings: []
      scheme: ""
      ## File to read bearer token for scraping targets
      bearerTokenFile: ""
      ## Secret to mount to read bearer token for scraping targets. The secret needs
      ## to be in the same namespace as the service monitor and accessible by the
      ## Prometheus Operator
      bearerTokenSecret: {}
        # name: secret-name
        # key:  key-name
      tlsConfig: {}

    ## selfMonitor endpoint
    metrics:
      interval: ""
      scrapeTimeout: ""
      proxyUrl: ""
      ## Whether to enable HTTP2 for servicemonitor
      enableHttp2: false
      honorLabels: false
      metricRelabelings: []
      relabelings: []
      scheme: ""
      ## File to read bearer token for scraping targets
      bearerTokenFile: ""
      ## Secret to mount to read bearer token for scraping targets. The secret needs
      ## to be in the same namespace as the service monitor and accessible by the
      ## Prometheus Operator
      bearerTokenSecret: {}
        # name: secret-name
        # key:  key-name
      tlsConfig: {}
  ## Create a scrapeConfig resource for scraping the kube-state-metrics service. Use this instead of serviceMonitor
  ## to have more instances of kube-state-metrics safety.
  scrapeconfig:
    ## To avoid duplicate metrics, first disable the serviceMonitor creation via prometheus.monitor.enabled=false
    enabled: false
    annotations: {}
    additionalLabels: {}
    jobName: kube-state-metrics
    ## SampleLimit defines per-scrape limit on number of scraped samples that will be accepted.
    ##
    sampleLimit: 0

    ## TargetLimit defines a limit on the number of scraped targets that will be accepted.
    ##
    targetLimit: 0

    ## Per-scrape limit on number of labels that will be accepted for a sample. Only valid in Prometheus versions 2.27.0 and newer.
    ##
    labelLimit: 0

    ## Per-scrape limit on length of labels name that will be accepted for a sample. Only valid in Prometheus versions 2.27.0 and newer.
    ##
    labelNameLengthLimit: 0

    ## Per-scrape limit on length of labels value that will be accepted for a sample. Only valid in Prometheus versions 2.27.0 and newer.
    ##
    labelValueLengthLimit: 0

    ## StaticConfigLabels defines the labels to be used in the Prometheus static configuration for scraping.
    staticConfigLabels: {}
    scrapeInterval: ""
    scrapeTimeout: ""
    proxyUrl: ""
    ## Whether to enable HTTP2 for scrapeconfig
    enableHttp2: false
    honorLabels: true
    metricRelabelings: []
    relabelings: []
    scheme: ""
    tlsConfig: {}

## Configure network policy for kube-state-metrics
networkPolicy:
  enabled: false
  # networkPolicy.flavor -- Flavor of the network policy to use.
  # Can be:
  # * kubernetes for networking.k8s.io/v1/NetworkPolicy
  # * cilium     for cilium.io/v2/CiliumNetworkPolicy
  flavor: kubernetes

  ## Configure the cilium network policy kube-apiserver selector
  # cilium:
    # kubeApiServerSelector:
      # - toEntities:
      #   - kube-apiserver

  # egress:
  # - {}
  # ingress:
  # - {}
  # podSelector:
  #   matchLabels:
  #     app.kubernetes.io/name: kube-state-metrics

securityContext:
  enabled: true
  runAsGroup: 65534
  runAsUser: 65534
  fsGroup: 65534
  runAsNonRoot: true
  seccompProfile:
    type: RuntimeDefault

## Specify security settings for a Container
## Allows overrides and additional options compared to (Pod) securityContext
## Ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-container
containerSecurityContext:
  readOnlyRootFilesystem: true
  allowPrivilegeEscalation: false
  capabilities:
    drop:
    - ALL

## Node labels for pod assignment
## Ref: https://kubernetes.io/docs/user-guide/node-selection/
nodeSelector: {}

## Affinity settings for pod assignment
## Can be defined as either a dict or string. String is useful for `tpl` templating.
## Ref: https://kubernetes.io/docs/concepts/configuration/assign-pod-node/
affinity: {}
# affinity: |
#   podAntiAffinity:
#     requiredDuringSchedulingIgnoredDuringExecution:
#       - labelSelector:
#           matchLabels:
#             {{- include "kube-state-metrics.selectorLabels" . | indent 10 }}
#         topologyKey: kubernetes.io/hostname

## Tolerations for pod assignment
## Ref: https://kubernetes.io/docs/concepts/configuration/taint-and-toleration/
tolerations: []

## Topology spread constraints for pod assignment
## Ref: https://kubernetes.io/docs/concepts/workloads/pods/pod-topology-spread-constraints/
topologySpreadConstraints: []

# Annotations to be added to the deployment/statefulset
annotations: {}

# Labels to be added to the deployment/statefulset
labels: {}

# Annotations to be added to the pod
podAnnotations: {}

# Labels to be added to the pod
podLabels: {}

## Assign a PriorityClassName to pods if set
# priorityClassName: ""

# Ref: https://kubernetes.io/docs/tasks/run-application/configure-pdb/
podDisruptionBudget: {}

# Comma-separated list of metrics to be exposed.
# This list comprises of exact metric names and/or regex patterns.
# The allowlist and denylist are mutually exclusive.
metricAllowlist: []

# Comma-separated list of metrics not to be enabled.
# This list comprises of exact metric names and/or regex patterns.
# The allowlist and denylist are mutually exclusive.
metricDenylist: []

# Comma-separated list of additional Kubernetes label keys that will be used in the resource's
# labels metric. By default the metric contains only name and namespace labels.
# To include additional labels, provide a list of resource names in their plural form and Kubernetes
# label keys you would like to allow for them (Example: '=namespaces=[k8s-label-1,k8s-label-n,...],pods=[app],...)'.
# A single '*' can be provided per resource instead to allow any labels, but that has
# severe performance implications (Example: '=pods=[*]').
metricLabelsAllowlist: []
  # - namespaces=[k8s-label-1,k8s-label-n]

# Comma-separated list of Kubernetes annotations keys that will be used in the resource'
# labels metric. By default the metric contains only name and namespace labels.
# To include additional annotations provide a list of resource names in their plural form and Kubernetes
# annotation keys you would like to allow for them (Example: '=namespaces=[kubernetes.io/team,...],pods=[kubernetes.io/team],...)'.
# A single '*' can be provided per resource instead to allow any annotations, but that has
# severe performance implications (Example: '=pods=[*]').
metricAnnotationsAllowList: []
  # - pods=[k8s-annotation-1,k8s-annotation-n]

# Available collectors for kube-state-metrics.
# By default, all available resources are enabled, comment out to disable.
collectors:
  - certificatesigningrequests
  - configmaps
  - cronjobs
  - daemonsets
  - deployments
  - endpoints
  - horizontalpodautoscalers
  - ingresses
  - jobs
  - leases
  - limitranges
  - mutatingwebhookconfigurations
  - namespaces
  - networkpolicies
  - nodes
  - persistentvolumeclaims
  - persistentvolumes
  - poddisruptionbudgets
  - pods
  - replicasets
  - replicationcontrollers
  - resourcequotas
  - secrets
  - services
  - statefulsets
  - storageclasses
  - validatingwebhookconfigurations
  - volumeattachments
  # - ingressclasses
  # - clusterrolebindings
  # - clusterroles
  # - roles

# Enabling kubeconfig will pass the --kubeconfig argument to the container
kubeconfig:
  enabled: false
  # base64 encoded kube-config file
  secret:

# Enabling support for customResourceState, will create a configMap including your config that will be read from kube-state-metrics
customResourceState:
  # Whether to enable support for CustomResourceStateMetrics.
  enabled: false

  # Whether to create the ConfigMap that holds the config.
  create: true

  # Name of the ConfigMap that holds the config. If empty, name will be generated based on the release name.
  name: ""

  # ConfigMap key that holds the config.
  key: config.yaml

  # Definition of the CustomResourceStateMetrics. Add (Cluster)Role permissions to list/watch the resources defined in the config to rbac.extraRules.
  config: {}

# Enable only the release namespace for collecting resources. By default all namespaces are collected.
# If releaseNamespace and namespaces are both set a merged list will be collected.
releaseNamespace: false

# Comma-separated list(string) or yaml list of namespaces to be enabled for collecting resources. By default all namespaces are collected.
namespaces: ""

# Comma-separated list of namespaces not to be enabled. If namespaces and namespaces-denylist are both set,
# only namespaces that are excluded in namespaces-denylist will be used.
namespacesDenylist: ""

## Override the deployment namespace
##
namespaceOverride: ""

resources: {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #  cpu: 100m
  #  memory: 64Mi
  # requests:
  #  cpu: 10m
  #  memory: 32Mi

# Enable self metrics configuration for service and Service Monitor
# Default values for telemetry configuration can be overridden
# If you set telemetryNodePort, you must also set service.type to NodePort
selfMonitor:
  enabled: false
  # telemetryHost: 0.0.0.0
  # telemetryPort: 8081
  # telemetryNodePort: 0

# Enable vertical pod autoscaler support for kube-state-metrics
verticalPodAutoscaler:
  enabled: false

  # Recommender responsible for generating recommendation for the object.
  # List should be empty (then the default recommender will generate the recommendation)
  # or contain exactly one recommender.
  # recommenders: []
  # - name: custom-recommender-performance

  # List of resources that the vertical pod autoscaler can control. Defaults to cpu and memory
  controlledResources: []
  # Specifies which resource values should be controlled: RequestsOnly or RequestsAndLimits.
  # controlledValues: RequestsAndLimits

  # Define the max allowed resources for the pod
  maxAllowed: {}
  # cpu: 200m
  # memory: 100Mi
  # Define the min allowed resources for the pod
  minAllowed: {}
  # cpu: 200m
  # memory: 100Mi

  # updatePolicy:
    # Specifies minimal number of replicas which need to be alive for VPA Updater to attempt pod eviction
    # minReplicas: 1
    # Specifies whether recommended updates are applied when a Pod is started and whether recommended updates
    # are applied during the life of a Pod. Possible values are "Off", "Initial", "Recreate", and "Auto".
    # updateMode: Auto

# volumeMounts are used to add custom volume mounts to deployment.
# See example below
volumeMounts: []
#  - mountPath: /etc/config
#    name: config-volume

# volumes are used to add custom volumes to deployment
# See example below
volumes: []
#  - configMap:
#      name: cm-for-volume
#    name: config-volume

# Extra manifests to deploy as an array
extraManifests: []
  # - apiVersion: v1
  #   kind: ConfigMap
  #   metadata:
  #   labels:
  #     name: prometheus-extra
  #   data:
  #     extra-data: "value"

## Containers allows injecting additional containers.
containers: []
  # - name: crd-init
  #   image: kiwigrid/k8s-sidecar:latest

## InitContainers allows injecting additional initContainers.
initContainers: []
  # - name: crd-sidecar
  #   image: kiwigrid/k8s-sidecar:latest

## dnsPolicy allows to change the default DNS configuration for the pod
## Ref: https://kubernetes.io/docs/concepts/services-networking/dns-pod-service/#pod-s-dns-policy
dnsPolicy: ClusterFirst

## dnsConfig allows setting up specific DNS configuration for the pod
## Ref: https://kubernetes.io/docs/concepts/services-networking/dns-pod-service/#pod-dns-config
dnsConfig: {}

## Settings for startup, liveness and readiness probes
## Ref: https://kubernetes.io/docs/tasks/configure-pod-container/configure-liveness-readiness-startup-probes/
##

## Startup probe can optionally be enabled.
##
startupProbe:
  enabled: false
  failureThreshold: 3
  httpGet:
    httpHeaders: []
    scheme: http
  initialDelaySeconds: 0
  periodSeconds: 10
  successThreshold: 1
  timeoutSeconds: 5

## Liveness probe
##
livenessProbe:
  failureThreshold: 3
  httpGet:
    httpHeaders: []
    scheme: http
  initialDelaySeconds: 5
  periodSeconds: 10
  successThreshold: 1
  timeoutSeconds: 5

## Readiness probe
##
readinessProbe:
  failureThreshold: 3
  httpGet:
    httpHeaders: []
    scheme: http
  initialDelaySeconds: 5
  periodSeconds: 10
  successThreshold: 1
  timeoutSeconds: 5
