---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.9.2
  creationTimestamp: null
  name: podlogs.monitoring.grafana.com
spec:
  group: monitoring.grafana.com
  names:
    categories:
    - grafana-alloy
    - alloy
    kind: PodLogs
    listKind: PodLogsList
    plural: podlogs
    singular: podlogs
  scope: Namespaced
  versions:
  - name: v1alpha2
    schema:
      openAPIV3Schema:
        description: PodLogs defines how to collect logs for a Pod.
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: PodLogsSpec defines how to collect logs for a Pod.
            properties:
              namespaceSelector:
                description: Selector to select which namespaces the Pod objects are
                  discovered from.
                properties:
                  matchExpressions:
                    description: matchExpressions is a list of label selector requirements.
                      The requirements are ANDed.
                    items:
                      description: A label selector requirement is a selector that
                        contains values, a key, and an operator that relates the key
                        and values.
                      properties:
                        key:
                          description: key is the label key that the selector applies
                            to.
                          type: string
                        operator:
                          description: operator represents a key's relationship to
                            a set of values. Valid operators are In, NotIn, Exists
                            and DoesNotExist.
                          type: string
                        values:
                          description: values is an array of string values. If the
                            operator is In or NotIn, the values array must be non-empty.
                            If the operator is Exists or DoesNotExist, the values
                            array must be empty. This array is replaced during a strategic
                            merge patch.
                          items:
                            type: string
                          type: array
                      required:
                      - key
                      - operator
                      type: object
                    type: array
                  matchLabels:
                    additionalProperties:
                      type: string
                    description: matchLabels is a map of {key,value} pairs. A single
                      {key,value} in the matchLabels map is equivalent to an element
                      of matchExpressions, whose key field is "key", the operator
                      is "In", and the values array contains only "value". The requirements
                      are ANDed.
                    type: object
                type: object
                x-kubernetes-map-type: atomic
              relabelings:
                description: RelabelConfigs to apply to logs before delivering.
                items:
                  description: 'RelabelConfig allows dynamic rewriting of the label
                    set, being applied to samples before ingestion. It defines `<metric_relabel_configs>`-section
                    of Prometheus configuration. More info: https://prometheus.io/docs/prometheus/latest/configuration/configuration/#metric_relabel_configs'
                  properties:
                    action:
                      default: replace
                      description: Action to perform based on regex matching. Default
                        is 'replace'. uppercase and lowercase actions require Prometheus
                        >= 2.36.
                      enum:
                      - replace
                      - Replace
                      - keep
                      - Keep
                      - drop
                      - Drop
                      - hashmod
                      - HashMod
                      - labelmap
                      - LabelMap
                      - labeldrop
                      - LabelDrop
                      - labelkeep
                      - LabelKeep
                      - lowercase
                      - Lowercase
                      - uppercase
                      - Uppercase
                      type: string
                    modulus:
                      description: Modulus to take of the hash of the source label
                        values.
                      format: int64
                      type: integer
                    regex:
                      description: Regular expression against which the extracted
                        value is matched. Default is '(.*)'
                      type: string
                    replacement:
                      description: Replacement value against which a regex replace
                        is performed if the regular expression matches. Regex capture
                        groups are available. Default is '$1'
                      type: string
                    separator:
                      description: Separator placed between concatenated source label
                        values. default is ';'.
                      type: string
                    sourceLabels:
                      description: The source labels select values from existing labels.
                        Their content is concatenated using the configured separator
                        and matched against the configured regular expression for
                        the replace, keep, and drop actions.
                      items:
                        description: LabelName is a valid Prometheus label name which
                          may only contain ASCII letters, numbers, as well as underscores.
                        pattern: ^[a-zA-Z_][a-zA-Z0-9_]*$
                        type: string
                      type: array
                    targetLabel:
                      description: Label to which the resulting value is written in
                        a replace action. It is mandatory for replace actions. Regex
                        capture groups are available.
                      type: string
                  type: object
                type: array
              selector:
                description: Selector to select Pod objects. Required.
                properties:
                  matchExpressions:
                    description: matchExpressions is a list of label selector requirements.
                      The requirements are ANDed.
                    items:
                      description: A label selector requirement is a selector that
                        contains values, a key, and an operator that relates the key
                        and values.
                      properties:
                        key:
                          description: key is the label key that the selector applies
                            to.
                          type: string
                        operator:
                          description: operator represents a key's relationship to
                            a set of values. Valid operators are In, NotIn, Exists
                            and DoesNotExist.
                          type: string
                        values:
                          description: values is an array of string values. If the
                            operator is In or NotIn, the values array must be non-empty.
                            If the operator is Exists or DoesNotExist, the values
                            array must be empty. This array is replaced during a strategic
                            merge patch.
                          items:
                            type: string
                          type: array
                      required:
                      - key
                      - operator
                      type: object
                    type: array
                  matchLabels:
                    additionalProperties:
                      type: string
                    description: matchLabels is a map of {key,value} pairs. A single
                      {key,value} in the matchLabels map is equivalent to an element
                      of matchExpressions, whose key field is "key", the operator
                      is "In", and the values array contains only "value". The requirements
                      are ANDed.
                    type: object
                type: object
                x-kubernetes-map-type: atomic
            required:
            - selector
            type: object
        type: object
    served: true
    storage: true
